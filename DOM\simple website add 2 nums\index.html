<html>
<style>
    .container {
        background-color: rgb(0, 42, 255);
        font-size: 100;
        padding: 20;
        margin: 100;
        color: rgb(157, 216, 232);
    }

    #container1 {
        background-color: rgb(118, 40, 207);
        font-size: 100;
        padding: 20;
        margin: 100;
        color: rgb(92, 17, 134);
    }
</style>

<script>
    function populateDiv() {
        const a = document.getElementById("firstNum").value;
        const b = document.getElementById("secondNum").value;
        const element = document.getElementById("finalSum");

        fetch("https://sum-server.100xdevs.com/sum?a="+ a + "&b=" + b)
        .then(function(response) {
            response.text()
                .then(function(ans) {
                    document.getElementById("finalSum").innerHTML = ans;
                })
        });

        let timeout;
        function debouncePopulatedDiv() {
            // how to cancel a clock -> clearTimeout
            clearTimeout(timeout);
            timeout = setTimeout(function () {
                populateDiv();
            }, 1000);
        }

        async function populateDiv2() {
            const a = document.getElementById("firstNum");
            const b = document.getElementById("secondNum");

            const response = await fetch("https://sum-server.100xdevs.com/sum?a="+ a + "&b=" + b);
            const ans = await response.text();
            document.getElementById("finalSum").innerHTML = ans;
        }

        // promise
        // const res = fetch("https://sum-server.100xdevs.com/sum?a="+ a + "&b=" + b);
        // console.log(res);
        // element.innerHTML = parseInt(a) + parseInt(b); // when we have to put it to the output 
    }
</script>

<body>
    <input onInput="populateDiv()" id="firstNum" type="text" placeholder="First number"></input>
    <br></br>
    <input id="secondNum" onInput="populateDiv()" type="text" placeholder="Second number"></input>
    <br></br>
    <button onclick="populateDiv()">Calculate sum</button>
    <div id="finalSum"></div>

    <!-- <div class="container">
        Hi 1
    </div>

    <div id="container1">
        Hi 2
    </div> -->
</body>
</html>