<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Currency Converter</title>
    <link href="style.css" rel="stylesheet" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
  </head>
  <body>
    <!-- <button id="btn">Get a Fact</button>
    <p id="fact"></p> -->

    <div class="container">
      <h2>Currency Converter</h2>
      <form>
        <div class="amount">
          <p>Enter Amount</p>
          <input value="1" type="text" />
        </div>
        <div class="dropdown">
          <div class="from">
            <p>From</p>
            <div class="select-container">
              <img src="https://flagsapi.com/US/flat/64.png" />
              <select name="from"></select>
            </div>
          </div>
          <i class="fa-solid fa-arrow-right-arrow-left"></i>
          <div class="to">
            <p>To</p>
            <div class="select-container">
              <img src="https://flagsapi.com/IN/flat/64.png" />
              <select name="to"></select>
            </div>
          </div>
        </div>
        <div class="msg">1USD = 83.5INR</div>
        <button>Get Exchange Rate</button>
      </form>
    </div>
    <script src="codes.js"></script>
    <script src="app.js"></script>
  </body>
</html>