<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rock Paper Scissors Game</title>
    <link rel="stylesheet" href ="style.css">
</head>
<body>
    
    <!-- Heading and images -->
    <h1>Rock Paper Scissors Game</h1>
    <div class="choices">
        <div class="choice" id="rock">
            <img src="./images/rock.png">
        </div>
        <div class="choice" id="paper">
            <img src="./images/paper.png">
        </div>
        <div class="choice" id="scissor">
            <img src="./images/scissor.png">
        </div>
    </div>

    <!-- Score board of user and computer -->
    <div class="Score-Board">
        <div class="score">
            <p id="user-score">0</p>
            <p>You</p>
        </div>
        <div>
            <p id="comp-score">0</p>
            <p>Computer</p>
        </div>
    </div>

    <!-- Message container for displaying the message -->
    <div class="msg-container">
        <p id="msg1">Play your move</p>
    </div>
    <script src="game.js"></script>
</body>
</html>