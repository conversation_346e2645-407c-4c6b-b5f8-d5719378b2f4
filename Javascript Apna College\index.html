<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Javascript</title>
  <link rel="stylesheet" href ="style.css">
</head>
<body>
  <div>
    This is a box
  </div>
  <script src="script.js"></script>
</body>
</html>

<!-- style tag used to connect HTML and CSS -->

 <!-- <div> Ohyeah
    <div>
      Day 5 of Javascript
    </div>
    <p> 
      Having fun!!
    </p>
  </div>
  
  <h1 id="heading">DOM taught by Apna College</h1>
  <h2 class="ClassHeading">Instructor: <PERSON><PERSON><PERSON></h2>
  <h3 class="ClassHeading">Student: <PERSON><PERSON></h3>
  <h4 class="ClassHeading">Javascript Session</h4>
  <button id="buttonbagr">Submit</button>
  <p>This session is helpful for Javascript</p>
  <p>This is a good session for beginners getting started into JS</p>

  <div>
      <h3>Brands</h3>
      <li>Blushberry</li>
      <li>H&M</li>
      <li>Roadster</li>
        <h2>Hello Javascript</h2>
  <div id="box" name="JSDiv">This is div
    <ul>List
      <li>Mango</li>
      <li>Grapes</li>
      <li>Apple</li>
    </ul>
  </div>

  <p class="para">This is simple line</p>
   </div> -->

    <!-- <p class="content">I am Paragraph</p> -->

  <!-- Events
    <button onclick="console.log('Button was selected');alert('You have selected')">Select</button>
    <button ondblclick="console.log('Button was clicked 2 times');alert('2 times clicked')">Double Click me</button>
    <div onmouseover="alert('You are in div')">
      This ia a div
    </div> -->

      <!-- 
  <button id="btn1">Select</button>
  <div>This is a box</div>
 -->

 <!-- <button id="mode">Change Mode</button>
  <p>Welcome to the website</p> -->