<html>
    <head>
        <title>
            ToDo Application
        </title>
    </head>
    <body>
            <input id="title" type="text" placeholder="ToDo title"></input>
            <br /><br />
            <input id="description" type="text" placeholder="ToDo description"></input>
            <br /><br />
            <button onclick="addToDo()">Add ToDo</button>
            <br /><br />
            <div id="todos"></div>
        
    <script>
            let globalId = 1;

            function markAsDone(todoId) {
            const parent = document.getElementById(todoId);
            parent.children[2].innerHTML = "Done!";
        }

            function createChild(title, description, id) {
            const child = document.createElement("div");

            const firstGrandParent = document.createElement("div");
            firstGrandParent.innerHTML = title;

            const secondGrandParent = document.createElement("div");
            secondGrandParent.innerHTML = description;

            const thirdGrandParent = document.createElement("button");
            thirdGrandParent.innerHTML = "Mark as done";
            thirdGrandParent.setAttribute("onclick", `markAsDone(${id})`);

            child.appendChild(firstGrandParent);
            child.appendChild(secondGrandParent);
            child.appendChild(thirdGrandParent);
            child.setAttribute("id", id);

        return child;
    }

            function addToDo() {
            const title = document.getElementById("title").value;
            const description = document.getElementById("description").value;

            if (!title || !description) {
                alert("Please fill out both fields!");
            return;
            }

            const parent = document.getElementById("todos");
            parent.appendChild(createChild(title, description, globalId++));


                // const orgHTML = document.getElementById("container").innerHTML;
                // // document.createElement
                // const childDiv = document.createElement("div");
                // childDiv.innerHTML = title;
                // document.getElementById("container").appendChild(childDiv);

                
                //It's ugly syntax, instead use document.createElement
                // document.getElementById("container").innerHTML = orgHTML + `
                // <div>
                //     <div>${title}</div>
                //     <div>${description}</div>
                //     <button>Mark as done</button>
                // </div>
                // `;
        } 
        </script>
    </body>
</html>


