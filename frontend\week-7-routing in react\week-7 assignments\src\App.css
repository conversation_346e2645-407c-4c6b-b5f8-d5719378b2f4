/* Assignment 1 */

.profile-container{
  width: 300px; 
  margin: 0 auto; 
  text-align: center; 
  background-color: #f0f0f0; 
  border-radius: 10px; 
  padding: 20px;
  height: 300px;
}

.profile-header { 
  background-color: #5c6fe7; 
  padding: 20px; 
  border-radius: 10px 10px 0 0; 
  height: 70px;
}

.profile-picture img { 
  width: 100px; 
  height: 100px; 
  border-radius: 50%;
  margin-top: 20px;
}

.profile-info h2 { 
  margin: 10px 0 5px; 
  font-size: 24px; 
  color: #fff;}

.profile-info span { 
  font-size: 18px; 
  color: #ccc; 
}

.profile-info{
  margin: 0;
  font-size: 13px;
  color: #fff;
}

.profile-stats{
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
  margin-top: 120px;
}

.stat h3 { 
  margin: 0; 
  font-size: 16px; 
} 

.stat p { 
  margin: 5px 0 0; 
  font-size: 14px; 
  color: #777; 
}


/* Assignment 2 */

/* Assignment 3 */

/* Assignment 4 */

/* Assignment 5 */
/* src/App.css */
.App {
  text-align: center;
  padding: 20px;
}

input {
  padding: 10px;
  margin: 10px;
}

button {
  padding: 10px;
  margin: 10px;
}

img {
  border-radius: 50%;
  margin: 10px;
}
