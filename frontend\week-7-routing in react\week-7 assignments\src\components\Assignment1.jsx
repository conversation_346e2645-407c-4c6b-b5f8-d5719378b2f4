import React from 'react';
import '../App.css';

function Assignment1() {

  return (
    <div className='profile-container'>
      <div className='profile-header'>
        <div className='profile-picture'>
          <img src="data:image/jpeg;base64,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" alt="Profile" />
        </div>
        <div className='profile-info'>
          <h2><b>Sejal Sarkar</b> <span>28</span></h2>
          <p>San Francisco</p>
        </div>
      </div>
      <div className="profile-stats">
        <div className='stat'>
            <h3>80k</h3>
            <p>Followers</p>
        </div>
        <div className='stat'>
            <h3>80.3k</h3>
            <p>Likes</p>
        </div>
        <div className='stat'>
            <h3>1.5k</h3>
            <p>Photos</p>
        </div>
      </div>
    </div>
  );
};

export default Assignment1;