## Notes for Tailwind CSS
 
- flex container in Tailwind CSS arranges its child elements in a column direction. Hence, we have to mention row, if we want elements one beside the other. 

- Tailwind CSS follows a mobile-first approach for responsive design, meaning styles are applied to smaller screens by default and can be overridden for larger screen sizes using breakpoints.

- Breakpoint	Prefix	Min Width
    sm	          sm:	≥ 640px
    md	          md:	≥ 768px
    lg	          lg:	≥ 1024px
    xl	          xl:	≥ 1280px
    2xl	          2xl:	≥ 1536px

