{"name": "mongodb-with-jwt", "version": "1.0.0", "description": "Same as the last assignment but you need to use jwts for authentication.\r have introduced the signgin endpoints for both users and admins.\r For this one, in every authenticated requests, you need to send the jwt in\r headers (Authorization : \"Bearer <actual token>\").\r You need to use mongodb to store all the data persistently.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"express": "^4.21.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.0"}}